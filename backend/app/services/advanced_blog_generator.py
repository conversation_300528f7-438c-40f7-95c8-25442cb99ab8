"""
Advanced Blog Generator Service
Generates SEO-optimized blogs following the complete guide for high-performance blogs in 2025
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field

import google.generativeai as genai
from app.core.config import settings

logger = logging.getLogger(__name__)

@dataclass
class BlogSection:
    """Represents a blog section with its content"""
    title: str
    content: str
    level: int  # H1=1, H2=2, H3=3, etc.
    section_type: str  # 'introduction', 'main', 'faq', 'conclusion'

@dataclass
class AdvancedBlogContent:
    """Complete blog content with all SEO + LLM elements"""
    # Core content (required)
    title: str
    meta_description: str
    introduction: str
    sections: List[BlogSection]
    faq: List[Dict[str, str]]
    conclusion: str

    # Existing SEO elements (required)
    primary_keyword: str
    secondary_keywords: List[str]
    schema_markup: Dict[str, Any]
    internal_links: List[Dict[str, str]]

    # E-E-A-T elements (required)
    author_bio: str
    sources: List[Dict[str, str]]
    statistics: List[Dict[str, Any]]

    # Metrics (required)
    word_count: int
    estimated_reading_time: str
    seo_score: float
    eeat_score: float

    # New LLM-optimized content blocks (optional with defaults)
    tldr: List[str] = field(default_factory=list)
    direct_answers: List[Dict[str, str]] = field(default_factory=list)
    ctas: List[Dict[str, str]] = field(default_factory=list)
    emma_insights: List[str] = field(default_factory=list)

    # Additional SEO metadata (optional)
    slug: str = ""
    title_tag: str = ""
    category: str = ""
    tags: List[str] = field(default_factory=list)
    publish_date: str = ""
    last_modified: str = ""

    # Schema payloads (optional)
    schema_payloads: Dict[str, Any] = field(default_factory=dict)  # {article, faq, howto?, entity?}
    related_posts: List[str] = field(default_factory=list)

    # E-E-A-T supplementary (optional)
    authoritative_sources: List[Dict[str, str]] = field(default_factory=list)

    # Validation & QA (optional)
    llm_citability_label: str = "Mejorable"
    seo_readiness_label: str = "Mejorable"
    validation_flags: List[str] = field(default_factory=list)
    qa_report: Dict[str, Any] = field(default_factory=dict)

class AdvancedBlogGenerator:
    """
    Advanced Blog Generator following the complete guide for high-performance blogs

    Generates blogs with:
    - Perfect H1 title formula (50-60 chars, keyword + benefit + year)
    - Irresistible introduction (150-200 words with hook, problem, solution, preview)
    - Modular content structure (H2 main sections, H3 subtopics)
    - Featured snippet optimization
    - E-E-A-T optimization
    - Schema markup generation
    - Quality validation checklist
    """

    def __init__(self, gemini_api_key: str = None):
        self.gemini_api_key = gemini_api_key or settings.GEMINI_API_KEY

        if self.gemini_api_key:
            genai.configure(api_key=self.gemini_api_key)
            self.gemini_model = genai.GenerativeModel('gemini-1.5-pro')
        else:
            self.gemini_model = None
            logger.warning("No Gemini API key provided - using fallback generation")

    async def generate_complete_blog(
        self,
        topic: str,
        primary_keyword: str,
        secondary_keywords: List[str],
        target_audience: str = "general",
        content_length: str = "long",  # short, medium, long
        language: str = "es"
    ) -> AdvancedBlogContent:
        """
        Generate a complete blog following the advanced guide

        Args:
            topic: Main topic for the blog
            primary_keyword: Primary keyword to optimize for
            secondary_keywords: List of secondary/LSI keywords
            target_audience: Target audience description
            content_length: Desired content length (short: 1200-1500, medium: 1500-2000, long: 2000-2500)
            language: Target language

        Returns:
            AdvancedBlogContent with complete blog structure
        """
        try:
            logger.info(f"🚀 Generating advanced blog for topic: {topic}")

            # Step 1: Generate optimized H1 title
            title = await self._generate_optimized_title(topic, primary_keyword)

            # Step 2: Generate meta description
            meta_description = await self._generate_meta_description(topic, primary_keyword)

            # Step 3: Generate irresistible introduction
            introduction = await self._generate_introduction(topic, primary_keyword, target_audience)

            # Step 4: Generate modular content sections
            sections = await self._generate_content_sections(
                topic, primary_keyword, secondary_keywords, target_audience, content_length
            )

            # Step 5: Generate FAQ section optimized for featured snippets
            faq = await self._generate_faq_section(topic, primary_keyword)

            # Step 6: Generate conclusion with CTA
            conclusion = await self._generate_conclusion(topic, primary_keyword)

            # Step 7: Generate E-E-A-T elements
            author_bio = self._generate_author_bio(topic)
            sources = await self._generate_sources(topic)
            statistics = await self._generate_statistics(topic)

            # Step 8: Generate schema markup
            schema_markup = self._generate_schema_markup(title, introduction, sections)

            # Step 9: Generate internal links suggestions
            internal_links = self._generate_internal_links(topic, primary_keyword)

            # Step 10: Calculate metrics
            full_content = f"{introduction}\n\n" + "\n\n".join([s.content for s in sections]) + f"\n\n{conclusion}"
            word_count = len(full_content.split())
            estimated_reading_time = f"{max(1, word_count // 200)} min"

            # Step 11: Calculate quality scores
            seo_score = self._calculate_seo_score(title, full_content, primary_keyword, secondary_keywords)
            eeat_score = self._calculate_eeat_score(full_content, sources, statistics)

            # Derive additional metadata
            current_iso = datetime.now().strftime("%Y-%m-%d")
            slug = primary_keyword.lower().replace(' ', '-')
            title_tag = title[:60]

            # Prepare layered schema payloads (backward compatible)
            schema_payloads = {
                "article": schema_markup,
                "faq": {
                    "@context": "https://schema.org",
                    "@type": "FAQPage",
                    "mainEntity": []
                }
            }

            # Validation and labels
            llm_label, seo_label, flags, qa = self._validate_and_label(
                tldr=[],
                direct_answers=[],
                faq=faq,
                statistics=statistics,
                sections=sections,
                title=title,
                meta_description=meta_description,
                word_count=word_count,
                internal_links=internal_links,
            )

            logger.info(f"✅ Advanced blog generated! Words: {word_count}, SEO Score: {seo_score:.1f}, E-E-A-T Score: {eeat_score:.1f}")

            return AdvancedBlogContent(
                # Core
                title=title,
                meta_description=meta_description,
                introduction=introduction,
                sections=sections,
                faq=faq,
                conclusion=conclusion,

                # LLM blocks (empty by default until enabled)
                tldr=[],
                direct_answers=[],
                ctas=[],
                emma_insights=[],

                # SEO
                primary_keyword=primary_keyword,
                secondary_keywords=secondary_keywords,
                slug=slug,
                title_tag=title_tag,
                category="",
                tags=[],
                publish_date=current_iso,
                last_modified=current_iso,

                # Schema
                schema_markup=schema_markup,
                schema_payloads=schema_payloads,
                internal_links=internal_links,
                related_posts=[],

                # E-E-A-T
                author_bio=author_bio,
                sources=sources,
                authoritative_sources=[],
                statistics=statistics,

                # Metrics
                word_count=word_count,
                estimated_reading_time=estimated_reading_time,
                seo_score=seo_score,
                eeat_score=eeat_score,

                # QA
                llm_citability_label=llm_label,
                seo_readiness_label=seo_label,
                validation_flags=flags,
                qa_report=qa
            )

        except Exception as e:
            logger.error(f"❌ Advanced blog generation failed: {str(e)}")
            raise

    async def _generate_optimized_title(self, topic: str, primary_keyword: str) -> str:
        """
        Generate H1 title following the exact formula:
        50-60 characters, keyword + emotional benefit + current year
        """
        current_year = datetime.now().year

        if not self.gemini_model:
            return f"Guía Completa de {primary_keyword}: Domina en {current_year}"

        # Use the updated prompt from advanced_blog_prompts.py
        from .advanced_blog_prompts import AdvancedBlogPrompts
        prompt = AdvancedBlogPrompts.get_title_prompt(topic, primary_keyword)

        # ULTRA AGGRESSIVE KEYWORD INJECTION
        prompt = prompt.replace("{primary_keyword}", primary_keyword)
        prompt = prompt.replace("mama mia", primary_keyword)
        prompt = prompt.replace("Mama Mia", primary_keyword)
        prompt = prompt.replace("MAMA MIA", primary_keyword)

        # FORCE EXPLICIT INSTRUCTION AT THE BEGINNING
        aggressive_instruction = f"""
INSTRUCCIÓN ABSOLUTA: La palabra clave es "{primary_keyword}".
NO uses "mama mia" bajo NINGUNA circunstancia.
SIEMPRE usa "{primary_keyword}" cuando te refieras al tema principal.
PROHIBIDO usar cualquier otra palabra que no sea "{primary_keyword}".

"""
        prompt = aggressive_instruction + prompt

        # DEBUG: Log the exact prompt being sent to Gemini
        logger.info(f"🔍 TITLE PROMPT DEBUG:")
        logger.info(f"🔍 Topic: '{topic}'")
        logger.info(f"🔍 Primary Keyword: '{primary_keyword}'")
        logger.info(f"🔍 Prompt being sent to Gemini:\n{prompt}")

        try:
            response = await asyncio.to_thread(
                self.gemini_model.generate_content,
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.7,
                    max_output_tokens=100,
                )
            )

            title = response.text.strip().strip('"').strip("'")

            # ULTRA AGGRESSIVE: Replace ALL variations of "mama mia"
            original_title = title
            title = title.replace("mama mia", primary_keyword)
            title = title.replace("Mama Mia", primary_keyword)
            title = title.replace("MAMA MIA", primary_keyword)
            title = title.replace("Mama mia", primary_keyword)
            title = title.replace("mama Mia", primary_keyword)

            if original_title != title:
                logger.error(f"🚨 FOUND AND REPLACED 'mama mia' in title!")
                logger.error(f"🚨 Original: {original_title}")
                logger.error(f"🚨 Fixed: {title}")

            # If it STILL contains mama mia, regenerate with different approach
            if "mama mia" in title.lower():
                logger.error(f"🚨 TITLE STILL CONTAINS 'mama mia' AFTER REPLACEMENT!")
                title = f"**Guía Completa de {primary_keyword}: Beneficios y Estrategias {datetime.now().year}**"

            # Validate length
            if len(title) > 60:
                title = title[:57] + "..."
            elif len(title) < 50:
                title = f"{title} - Guía {current_year}"

            return title

        except Exception as e:
            logger.error(f"Title generation failed: {str(e)}")
            return f"Guía Completa de {primary_keyword}: Domina en {current_year}"

    async def _generate_meta_description(self, topic: str, primary_keyword: str) -> str:
        """Generate optimized meta description (150-160 characters)"""
        if not self.gemini_model:
            return f"Descubre todo sobre {primary_keyword}. Guía completa con estrategias probadas y resultados garantizados. ¡Empieza hoy!"

        # Use the updated prompt from advanced_blog_prompts.py
        from .advanced_blog_prompts import AdvancedBlogPrompts
        prompt = AdvancedBlogPrompts.get_meta_description_prompt(topic, primary_keyword)

        try:
            response = await asyncio.to_thread(
                self.gemini_model.generate_content,
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.7,
                    max_output_tokens=100,
                )
            )

            meta_desc = response.text.strip().strip('"').strip("'")

            # CRITICAL: Replace "mama mia" with actual keyword if it appears
            if "mama mia" in meta_desc.lower():
                logger.warning(f"⚠️ Found 'mama mia' in meta description, replacing with '{primary_keyword}'")
                meta_desc = meta_desc.replace("mama mia", primary_keyword).replace("Mama Mia", primary_keyword)

            # Validate length
            if len(meta_desc) > 160:
                meta_desc = meta_desc[:157] + "..."

            return meta_desc

        except Exception as e:
            logger.error(f"Meta description generation failed: {str(e)}")
            return f"Descubre todo sobre {primary_keyword}. Guía completa con estrategias probadas. ¡Empieza hoy!"

    async def _generate_introduction(self, topic: str, primary_keyword: str, target_audience: str) -> str:
        """
        Generate irresistible introduction following the exact formula:
        150-200 words with hook, problem identification, solution promise, content preview
        """
        if not self.gemini_model:
            return f"""¿Sabías que el 89% de las empresas que dominan {primary_keyword} generan 67% más resultados?

Si tu estrategia de {primary_keyword} no está dando los resultados que necesitas, el problema no es la falta de esfuerzo, sino la falta de metodología correcta.

En esta guía completa te mostraré las técnicas exactas que han ayudado a más de 500 empresas a triplicar sus resultados en menos de 6 meses.

Aprenderás desde los fundamentos hasta estrategias avanzadas, con ejemplos reales y plantillas descargables."""

        # Use the updated prompt from advanced_blog_prompts.py
        from .advanced_blog_prompts import AdvancedBlogPrompts
        prompt = AdvancedBlogPrompts.get_introduction_prompt(topic, primary_keyword, target_audience)

        # CRITICAL: Force keyword injection - replace any remaining placeholders
        prompt = prompt.replace("{primary_keyword}", primary_keyword)
        prompt = prompt.replace("mama mia", primary_keyword)
        prompt = prompt.replace("Mama Mia", primary_keyword)

        try:
            response = await asyncio.to_thread(
                self.gemini_model.generate_content,
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.7,
                    max_output_tokens=300,
                )
            )

            introduction = response.text.strip()

            # CRITICAL: Replace "mama mia" with actual keyword if it appears
            if "mama mia" in introduction.lower():
                logger.warning(f"⚠️ Found 'mama mia' in introduction, replacing with '{primary_keyword}'")
                introduction = introduction.replace("mama mia", primary_keyword).replace("Mama Mia", primary_keyword)

            # Validate word count (150-200 words)
            word_count = len(introduction.split())
            if word_count < 150:
                introduction += f"\n\nEsta metodología ha sido probada por expertos en {primary_keyword} y garantiza resultados medibles desde el primer día."
            elif word_count > 200:
                words = introduction.split()
                introduction = " ".join(words[:200]) + "..."

            return introduction

        except Exception as e:
            logger.error(f"Introduction generation failed: {str(e)}")
            return f"""¿Sabías que el 89% de las empresas que dominan {primary_keyword} generan 67% más resultados?

Si tu estrategia de {primary_keyword} no está dando los resultados que necesitas, el problema no es la falta de esfuerzo, sino la falta de metodología correcta.

En esta guía completa te mostraré las técnicas exactas que han ayudado a más de 500 empresas a triplicar sus resultados en menos de 6 meses.

Aprenderás desde los fundamentos hasta estrategias avanzadas, con ejemplos reales y plantillas descargables."""

    async def _generate_content_sections(
        self,
        topic: str,
        primary_keyword: str,
        secondary_keywords: List[str],
        target_audience: str,
        content_length: str
    ) -> List[BlogSection]:
        """
        Generate modular content sections following the guide structure:
        H2: Main sections (4-7 per article)
        H3: Subtopics within H2 (2-4 per H2)
        """
        # Define section templates based on the guide (NO PLACEHOLDERS)
        section_templates = [
            {
                "title": f"¿Qué es {primary_keyword}? (Definición Clara)",
                "type": "definition"
            },
            {
                "title": f"7 Beneficios Principales de {primary_keyword}",
                "type": "benefits"
            },
            {
                "title": f"Cómo Implementar {primary_keyword} - Guía Paso a Paso",
                "type": "how_to"
            },
            {
                "title": f"Mejores Prácticas y Errores Comunes en {primary_keyword}",
                "type": "best_practices"
            },
            {
                "title": f"Casos de Uso Reales de {primary_keyword}",
                "type": "case_studies"
            },
            {
                "title": f"Herramientas y Recursos para {primary_keyword}",
                "type": "tools"
            }
        ]

        # Select sections based on content length
        if content_length == "short":
            selected_templates = section_templates[:4]
        elif content_length == "medium":
            selected_templates = section_templates[:5]
        else:  # long
            selected_templates = section_templates

        sections = []

        for template in selected_templates:
            section_content = await self._generate_section_content(
                template, topic, primary_keyword, secondary_keywords, target_audience
            )

            # Create main section (H2) - NO MORE SUBSECTIONS
            main_section = BlogSection(
                title=template["title"],
                content=section_content["main_content"],
                level=2,
                section_type="main"
            )
            sections.append(main_section)

            # NO MORE SUBSECTIONS - just use main content

        return sections

    async def _generate_section_content(
        self,
        template: Dict[str, Any],
        topic: str,
        primary_keyword: str,
        secondary_keywords: List[str],
        target_audience: str
    ) -> Dict[str, Any]:
        """Generate content for a specific section based on template"""
        if not self.gemini_model:
            return self._generate_fallback_section_content(template, primary_keyword)

        # Use the updated prompt from advanced_blog_prompts.py
        from .advanced_blog_prompts import AdvancedBlogPrompts
        prompt = AdvancedBlogPrompts.get_section_content_prompt(
            template['title'],
            template['type'],
            topic,
            primary_keyword,
            secondary_keywords,
            target_audience
        )

        # ULTRA AGGRESSIVE KEYWORD INJECTION
        prompt = prompt.replace("{primary_keyword}", primary_keyword)
        prompt = prompt.replace("mama mia", primary_keyword)
        prompt = prompt.replace("Mama Mia", primary_keyword)
        prompt = prompt.replace("MAMA MIA", primary_keyword)

        # FORCE EXPLICIT INSTRUCTION AT THE BEGINNING
        aggressive_instruction = f"""
INSTRUCCIÓN ABSOLUTA: La palabra clave es "{primary_keyword}".
NO uses "mama mia" bajo NINGUNA circunstancia.
SIEMPRE usa "{primary_keyword}" cuando te refieras al tema principal.
PROHIBIDO usar cualquier otra palabra que no sea "{primary_keyword}".
El tema del contenido es sobre "{primary_keyword}", no sobre "mama mia".

"""
        prompt = aggressive_instruction + prompt

        # DEBUG: Log the exact prompt being sent to Gemini
        logger.info(f"🔍 SECTION PROMPT DEBUG:")
        logger.info(f"🔍 Section Title: '{template['title']}'")
        logger.info(f"🔍 Section Type: '{template['type']}'")
        logger.info(f"🔍 Topic: '{topic}'")
        logger.info(f"🔍 Primary Keyword: '{primary_keyword}'")
        logger.info(f"🔍 Secondary Keywords: {secondary_keywords}")
        logger.info(f"🔍 Target Audience: '{target_audience}'")
        logger.info(f"🔍 Prompt being sent to Gemini:\n{prompt}")

        try:
            response = await asyncio.to_thread(
                self.gemini_model.generate_content,
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.7,
                    max_output_tokens=800,
                )
            )

            content = response.text.strip()

            # DEBUG: Log the raw response from Gemini
            logger.info(f"🔍 GEMINI RESPONSE DEBUG:")
            logger.info(f"🔍 Raw response from Gemini:\n{content}")

            # ULTRA AGGRESSIVE: Replace ALL variations of "mama mia"
            original_content = content
            content = content.replace("mama mia", primary_keyword)
            content = content.replace("Mama Mia", primary_keyword)
            content = content.replace("MAMA MIA", primary_keyword)
            content = content.replace("Mama mia", primary_keyword)
            content = content.replace("mama Mia", primary_keyword)

            if original_content != content:
                logger.error(f"🚨 FOUND AND REPLACED 'mama mia' in section content!")
                logger.error(f"🚨 Section: {template['title']}")
                logger.error(f"🚨 Replacements made: {original_content.count('mama mia') + original_content.count('Mama Mia')}")

            # If it STILL contains mama mia, log critical error
            if "mama mia" in content.lower():
                logger.error(f"🚨 SECTION CONTENT STILL CONTAINS 'mama mia' AFTER REPLACEMENT!")
                logger.error(f"🚨 Content preview: {content[:300]}...")

            # SIMPLIFIED: Just use all content as main content
            return {
                "main_content": content,
                "subsections": []  # Always empty
            }

        except Exception as e:
            logger.error(f"Section content generation failed: {str(e)}")
            return self._generate_fallback_section_content(template, primary_keyword)

    def _generate_fallback_section_content(self, template: Dict[str, Any], primary_keyword: str) -> Dict[str, Any]:
        """Generate fallback content when AI generation fails"""
        return {
            "main_content": f"Esta sección cubre aspectos importantes de {primary_keyword} que todo profesional debe conocer. Incluye estrategias probadas y mejores prácticas del sector.",
            "subsections": []  # NO MORE SUBSECTIONS
        }

    async def _generate_faq_section(self, topic: str, primary_keyword: str) -> List[Dict[str, str]]:
        """
        Generate FAQ section optimized for featured snippets
        Format: H3 as question, 50-80 word answers
        """
        if not self.gemini_model:
            return [
                {
                    "question": f"¿Qué es {primary_keyword}?",
                    "answer": f"{primary_keyword} es una estrategia fundamental que permite a las empresas mejorar significativamente sus resultados. Se basa en metodologías probadas y se adapta a diferentes tipos de organizaciones."
                },
                {
                    "question": f"¿Cómo empezar con {primary_keyword}?",
                    "answer": f"Para empezar con {primary_keyword}, primero define tus objetivos específicos, analiza tu situación actual, y desarrolla un plan paso a paso. Es recomendable comenzar con proyectos pequeños para validar la metodología."
                },
                {
                    "question": f"¿Cuánto tiempo toma ver resultados con {primary_keyword}?",
                    "answer": f"Los primeros resultados de {primary_keyword} suelen verse entre 2-4 semanas, mientras que los resultados significativos aparecen típicamente entre 2-3 meses de implementación consistente."
                },
                {
                    "question": f"¿Cuáles son los errores más comunes en {primary_keyword}?",
                    "answer": f"Los errores más comunes incluyen falta de planificación inicial, no medir resultados regularmente, y no adaptar la estrategia según los datos obtenidos. También es común subestimar el tiempo necesario."
                }
            ]

        # Use the updated prompt from advanced_blog_prompts.py
        from .advanced_blog_prompts import AdvancedBlogPrompts
        prompt = AdvancedBlogPrompts.get_faq_prompt(topic, primary_keyword)

        # CRITICAL: Force keyword injection - replace any remaining placeholders
        prompt = prompt.replace("{primary_keyword}", primary_keyword)
        prompt = prompt.replace("mama mia", primary_keyword)
        prompt = prompt.replace("Mama Mia", primary_keyword)

        try:
            response = await asyncio.to_thread(
                self.gemini_model.generate_content,
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.7,
                    max_output_tokens=600,
                )
            )

            content = response.text.strip()

            # CRITICAL: Replace "mama mia" with actual keyword if it appears
            if "mama mia" in content.lower():
                logger.warning(f"⚠️ Found 'mama mia' in FAQ content, replacing with '{primary_keyword}'")
                content = content.replace("mama mia", primary_keyword).replace("Mama Mia", primary_keyword)

            faqs = []

            # Parse the new format which has questions in bold and answers following
            lines = content.split('\n')
            current_question = None
            current_answer_lines = []

            for line in lines:
                line = line.strip()

                # Skip empty lines and section headers
                if not line or line.startswith('**Preguntas Frecuentes'):
                    continue

                # Check if this is a question (starts with ** and ends with **)
                if line.startswith('**') and line.endswith('**') and '?' in line:
                    # Save previous FAQ if exists
                    if current_question and current_answer_lines:
                        answer = ' '.join(current_answer_lines).strip()
                        if answer:
                            faqs.append({
                                "question": current_question.strip('*'),
                                "answer": answer
                            })

                    # Start new question
                    current_question = line.strip('*')
                    current_answer_lines = []

                # If we have a current question, this line is part of the answer
                elif current_question and line:
                    current_answer_lines.append(line)

            # Add the last FAQ if exists
            if current_question and current_answer_lines:
                answer = ' '.join(current_answer_lines).strip()
                if answer:
                    faqs.append({
                        "question": current_question.strip('*'),
                        "answer": answer
                    })

            return faqs if faqs else self._generate_fallback_faq(primary_keyword)

        except Exception as e:
            logger.error(f"FAQ generation failed: {str(e)}")
            return self._generate_fallback_faq(primary_keyword)

    def _generate_fallback_faq(self, primary_keyword: str) -> List[Dict[str, str]]:
        """Generate fallback FAQ when AI generation fails"""
        return [
            {
                "question": f"¿Qué es {primary_keyword}?",
                "answer": f"{primary_keyword} es una estrategia fundamental que permite a las empresas mejorar significativamente sus resultados mediante metodologías probadas y adaptables a diferentes organizaciones."
            },
            {
                "question": f"¿Cómo implementar {primary_keyword} correctamente?",
                "answer": f"Para implementar {primary_keyword} correctamente, define objetivos específicos, analiza la situación actual, desarrolla un plan detallado y ejecuta con medición constante de resultados."
            },
            {
                "question": f"¿Cuánto tiempo toma ver resultados con {primary_keyword}?",
                "answer": f"Los resultados iniciales de {primary_keyword} aparecen entre 2-4 semanas, mientras que los resultados significativos se observan típicamente entre 2-3 meses de implementación."
            }
        ]

    async def _generate_conclusion(self, topic: str, primary_keyword: str) -> str:
        """Generate conclusion with key points summary and clear CTA"""
        if not self.gemini_model:
            return f"""**Conclusión: Transforma Tu Estrategia de {primary_keyword}**

En esta guía completa sobre {primary_keyword}, hemos cubierto los aspectos más importantes que necesitas conocer para implementar esta estrategia exitosamente.

**Puntos clave a recordar:**
• **Definición clara** y beneficios específicos de {primary_keyword}
• **Metodología paso a paso** para la implementación
• **Mejores prácticas** y errores comunes a evitar
• **Herramientas y recursos** recomendados

**Próximos Pasos**

Ahora que conoces los fundamentos de {primary_keyword}, es momento de **pasar a la acción**. Comienza implementando las estrategias más básicas y ve escalando gradualmente.

**¿Necesitas ayuda personalizada** con tu estrategia de {primary_keyword}? Nuestro equipo de expertos puede ayudarte a desarrollar un **plan específico** para tu empresa."""

        # Use the updated prompt from advanced_blog_prompts.py
        from .advanced_blog_prompts import AdvancedBlogPrompts
        prompt = AdvancedBlogPrompts.get_conclusion_prompt(topic, primary_keyword)

        try:
            response = await asyncio.to_thread(
                self.gemini_model.generate_content,
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.7,
                    max_output_tokens=400,
                )
            )

            conclusion = response.text.strip()
            return conclusion

        except Exception as e:
            logger.error(f"Conclusion generation failed: {str(e)}")
            return f"""**Conclusión: Transforma Tu Estrategia de {primary_keyword}**

En esta guía completa sobre {primary_keyword}, hemos cubierto los aspectos más importantes para implementar esta estrategia exitosamente.

**Puntos clave a recordar:**
• **Fundamentos y beneficios** de {primary_keyword}
• **Metodología paso a paso** para la implementación
• **Mejores prácticas** y errores a evitar
• **Herramientas y recursos** recomendados

**Próximos Pasos**

Comienza implementando las **estrategias básicas** y escala gradualmente según tus resultados.

**¿Necesitas ayuda personalizada?** Nuestro equipo puede desarrollar un **plan específico** para tu empresa."""

    def _generate_author_bio(self, topic: str) -> str:
        """Generate author bio with expertise credentials"""
        return f"""**Sobre el Autor:** Especialista en {topic} con más de 5 años de experiencia ayudando a empresas a optimizar sus estrategias digitales. Ha trabajado con más de 200 organizaciones de diferentes sectores, logrando mejoras promedio del 300% en métricas clave. Certificado en las principales plataformas y metodologías del sector."""

    async def _generate_sources(self, topic: str) -> List[Dict[str, str]]:
        """Generate credible sources for E-E-A-T"""
        # In a real implementation, this would fetch actual sources
    def _validate_and_label(
        self,
        tldr: List[str],
        direct_answers: List[Dict[str, str]],
        faq: List[Dict[str, str]],
        statistics: List[Dict[str, Any]],
        sections: List[BlogSection],
        title: str,
        meta_description: str,
        word_count: int,
        internal_links: List[Dict[str, str]],
    ) -> Tuple[str, str, List[str], Dict[str, Any]]:
        """Apply acceptance-criteria checks and return labels + flags + QA summary."""
        flags: List[str] = []

        # Presence checks
        if not tldr:
            flags.append("missing_tldr")
        if not direct_answers:
            flags.append("missing_direct_answers")
        if not faq or len(faq) < 3:
            flags.append("missing_faq")
        if not statistics or len(statistics) < 2:
            flags.append("missing_statistics")
        if word_count < 1200:
            flags.append("low_word_count")
        if len(internal_links) < 3:
            flags.append("weak_internal_linking")
        if not title or len(title) < 20:
            flags.append("weak_title")
        if not meta_description or len(meta_description) < 120:
            flags.append("weak_meta_description")

        # Simple label logic
        llm_label = "Excelente"
        seo_label = "Excelente"
        if flags:
            # downgrade based on flag count
            if len(flags) >= 4:
                llm_label = "Mejorable"
                seo_label = "Mejorable"
            else:
                llm_label = "Bueno"
                seo_label = "Bueno"

        qa_report = {
            "summary": {
                "llm_citability": llm_label,
                "seo_readiness": seo_label,
                "word_count": word_count,
                "sections": len(sections),
                "faq": len(faq),
                "statistics": len(statistics),
                "internal_links": len(internal_links)
            },
            "flags": flags,
            "recommendations": [
                "Añadir TL;DR (3–6 bullets)" if "missing_tldr" in flags else "",
                "Añadir Direct Answers (2–5 Q→A de 40–120 palabras)" if "missing_direct_answers" in flags else "",
                "Añadir 3–7 FAQs" if "missing_faq" in flags else "",
                "Incluir 2–5 estadísticas con fuente+año" if "missing_statistics" in flags else "",
                "Incrementar profundidad a ≥1,200 palabras" if "low_word_count" in flags else "",
                "Añadir ≥3 enlaces internos contextuales" if "weak_internal_linking" in flags else "",
                "Mejorar title/meta para cubrir beneficio + keyword" if "weak_title" in flags or "weak_meta_description" in flags else "",
            ]
        }

        # Remove empty recommendations
        qa_report["recommendations"] = [r for r in qa_report["recommendations"] if r]

        return llm_label, seo_label, flags, qa_report

    async def _generate_statistics(self, topic: str) -> List[Dict[str, Any]]:
        """Generate relevant statistics for the topic"""
        current_year = datetime.now().year
        return [
            {
                "statistic": "89% de las empresas reportan mejores resultados",
                "source": f"Estudio del sector {current_year}",
                "context": f"Empresas que implementan estrategias de {topic} correctamente"
            },
            {
                "statistic": "300% de incremento promedio en métricas clave",
                "source": "Análisis de casos de éxito",
                "context": f"Resultados obtenidos en los primeros 6 meses de implementación"
            },
            {
                "statistic": "67% reducción en tiempo de implementación",
                "source": "Comparativa metodológica",
                "context": f"Usando metodologías optimizadas vs. enfoques tradicionales"
            }
        ]

    def _generate_schema_markup(self, title: str, introduction: str, sections: List[BlogSection]) -> Dict[str, Any]:
        """Generate Schema.org markup for the blog"""
        current_date = datetime.now().isoformat()

        # Create FAQ schema from sections
        faq_items = []
        for section in sections:
            if section.level == 3 and "?" in section.title:
                faq_items.append({
                    "@type": "Question",
                    "name": section.title,
                    "acceptedAnswer": {
                        "@type": "Answer",
                        "text": section.content[:200] + "..." if len(section.content) > 200 else section.content
                    }
                })

        schema = {
            "@context": "https://schema.org",
            "@type": "Article",
            "headline": title,
            "author": {
                "@type": "Person",
                "name": "Especialista Emma Studio"
            },
            "datePublished": current_date,
            "dateModified": current_date,
            "description": introduction[:160],
            "publisher": {
                "@type": "Organization",
                "name": "Emma Studio",
                "logo": {
                    "@type": "ImageObject",
                    "url": "https://emma-studio.com/logo.png"
                }
            }
        }

        if faq_items:
            schema["mainEntity"] = {
                "@type": "FAQPage",
                "mainEntity": faq_items
            }

        return schema

    def _generate_internal_links(self, topic: str, primary_keyword: str) -> List[Dict[str, str]]:
        """Generate internal link suggestions"""
        return [
            {
                "anchor_text": f"Guía completa de {primary_keyword}",
                "url": f"/blog/{primary_keyword.lower().replace(' ', '-')}-guia-completa",
                "context": "Enlace hacia contenido relacionado"
            },
            {
                "anchor_text": f"Herramientas para {primary_keyword}",
                "url": f"/blog/herramientas-{primary_keyword.lower().replace(' ', '-')}",
                "context": "Recursos y herramientas recomendadas"
            },
            {
                "anchor_text": f"Casos de éxito en {primary_keyword}",
                "url": f"/blog/casos-exito-{primary_keyword.lower().replace(' ', '-')}",
                "context": "Ejemplos reales de implementación"
            }
        ]

    def _calculate_seo_score(
        self,
        title: str,
        content: str,
        primary_keyword: str,
        secondary_keywords: List[str]
    ) -> float:
        """Calculate SEO score based on optimization factors"""
        score = 0

        # Title optimization (20 points)
        if primary_keyword.lower() in title.lower():
            score += 10
        if 50 <= len(title) <= 60:
            score += 10

        # Keyword density (20 points)
        content_lower = content.lower()
        primary_count = content_lower.count(primary_keyword.lower())
        word_count = len(content.split())

        if word_count > 0:
            density = (primary_count / word_count) * 100
            if 0.5 <= density <= 1.5:  # Optimal density
                score += 20
            elif 0.3 <= density <= 2.0:  # Acceptable density
                score += 15
            else:
                score += 5

        # Content structure (20 points)
        if '<h2>' in content:
            score += 5
        if '<h3>' in content:
            score += 5
        if '<ul>' in content or '<ol>' in content:
            score += 5
        if word_count >= 1200:
            score += 5

        # Secondary keywords (15 points)
        secondary_found = sum(1 for kw in secondary_keywords if kw.lower() in content_lower)
        score += min(15, secondary_found * 3)

        # Featured snippet optimization (15 points)
        if '?' in content:  # FAQ format
            score += 5
        if 'qué es' in content_lower:  # Definition format
            score += 5
        if any(phrase in content_lower for phrase in ['paso 1', 'paso 2', 'primero', 'segundo']):
            score += 5

        # Internal structure (10 points)
        if 'conclusión' in content_lower:
            score += 3
        if 'introducción' in content_lower or content.startswith('¿'):
            score += 3
        if 'preguntas frecuentes' in content_lower or 'faq' in content_lower:
            score += 4

        return min(100, score)

    def _calculate_eeat_score(
        self,
        content: str,
        sources: List[Dict[str, str]],
        statistics: List[Dict[str, Any]]
    ) -> float:
        """Calculate E-E-A-T score based on expertise, authoritativeness, trustworthiness"""
        score = 0
        content_lower = content.lower()

        # Experience (25 points)
        experience_indicators = [
            'experiencia', 'he trabajado', 'en mi experiencia', 'años de',
            'he ayudado', 'casos de éxito', 'resultados obtenidos'
        ]
        experience_found = sum(1 for indicator in experience_indicators if indicator in content_lower)
        score += min(25, experience_found * 4)

        # Expertise (25 points)
        expertise_indicators = [
            'certificado', 'especialista', 'experto', 'metodología',
            'técnica', 'análisis', 'investigación', 'estudio'
        ]
        expertise_found = sum(1 for indicator in expertise_indicators if indicator in content_lower)
        score += min(25, expertise_found * 3)

        # Authoritativeness (25 points)
        # Sources and references
        score += min(15, len(sources) * 5)
        # Statistics and data
        score += min(10, len(statistics) * 3)

        # Trustworthiness (25 points)
        trust_indicators = [
            'fuente', 'según', 'estudio', 'investigación', 'datos',
            'estadística', 'informe', 'análisis', 'verificado'
        ]
        trust_found = sum(1 for indicator in trust_indicators if indicator in content_lower)
        score += min(25, trust_found * 3)

        return min(100, score)

    def get_renderable_blog_content(self, blog: AdvancedBlogContent) -> Dict[str, Any]:
        """
        Prepara SOLO el contenido visible para el frontend/usuario final.

        Esta función filtra y formatea el contenido para que el usuario vea:
        ✅ Contenido editorial profesional
        ❌ Sin metadatos técnicos, scores, o elementos de backend

        Args:
            blog: AdvancedBlogContent completo con todos los elementos

        Returns:
            Dict con solo los elementos que debe ver el usuario final
        """
        current_date = datetime.now().strftime("%d de %B de %Y")

        # CRITICAL: Clean content before showing to user
        cleaned_content = self._clean_content_for_user(blog)

        return {
            # ✅ Contenido principal visible (LIMPIO)
            "title": cleaned_content["title"],
            "introduction": cleaned_content["introduction"],
            "sections": cleaned_content["sections"],
            "faq": cleaned_content["faq"],
            "conclusion": cleaned_content["conclusion"],

            # ✅ Información editorial visible
            "author_name": "Especialista Emma Studio",
            "author_bio": cleaned_content["author_bio"],
            "published_date": current_date,
            "estimated_reading_time": cleaned_content["estimated_reading_time"],

            # ✅ Referencias y fuentes visibles
            "sources": cleaned_content["sources"],
            "statistics": cleaned_content["statistics"],

            # ✅ Enlaces internos (solo los anchor_text visibles)
            "internal_links": [
                {
                    "text": link["anchor_text"],
                    "url": link["url"],
                    "description": link.get("context", "")
                }
                for link in blog.internal_links
            ],

            # ❌ NO se incluyen elementos técnicos:
            # - schema_markup (va en <script> del <head>)
            # - seo_score, eeat_score (solo para validación interna)
            # - meta_description (va en <meta> del <head>)
            # - primary_keyword, secondary_keywords (solo para SEO)
        }

    def get_seo_metadata(self, blog: AdvancedBlogContent) -> Dict[str, Any]:
        """
        Prepara SOLO los metadatos técnicos para SEO (no visibles al usuario).

        Estos elementos van en el <head> o se usan para validación interna.

        Args:
            blog: AdvancedBlogContent completo

        Returns:
            Dict con elementos técnicos para SEO y backend
        """
        return {
            # 🔧 Metadatos técnicos
            "meta_description": blog.meta_description,
            "schema_markup": blog.schema_markup,
            "primary_keyword": blog.primary_keyword,
            "secondary_keywords": blog.secondary_keywords,

            # 📊 Métricas de calidad (para dashboard interno)
            "seo_score": blog.seo_score,
            "eeat_score": blog.eeat_score,
            "word_count": blog.word_count,

            # 🔗 URLs y slugs
            "url_slug": blog.primary_keyword.lower().replace(' ', '-'),
            "canonical_url": f"/blog/{blog.primary_keyword.lower().replace(' ', '-')}"
        }

    def _clean_content_for_user(self, blog: AdvancedBlogContent) -> Dict[str, Any]:
        """
        CRITICAL: Clean content before showing to user.

        Removes:
        - Placeholder text like "Beneficio 2 + caso de uso"
        - Duplicate titles
        - Technical instructions
        - Repetitive keyword usage
        - Empty sections
        """
        # Clean title
        cleaned_title = self._clean_title(blog.title, blog.primary_keyword)

        # Clean introduction
        cleaned_introduction = self._clean_text_content(blog.introduction, blog.primary_keyword)

        # Clean sections
        cleaned_sections = []
        for section in blog.sections:
            cleaned_section = self._clean_section(section, blog.primary_keyword)
            if cleaned_section:  # Only add non-empty sections
                cleaned_sections.append(cleaned_section)

        # Clean FAQ
        cleaned_faq = self._clean_faq(blog.faq, blog.primary_keyword)

        # Clean conclusion
        cleaned_conclusion = self._clean_text_content(blog.conclusion, blog.primary_keyword)

        return {
            "title": cleaned_title,
            "introduction": cleaned_introduction,
            "sections": cleaned_sections,
            "faq": cleaned_faq,
            "conclusion": cleaned_conclusion,
            "author_bio": blog.author_bio,
            "sources": blog.sources,
            "statistics": blog.statistics,
            "word_count": blog.word_count,
            "estimated_reading_time": blog.estimated_reading_time
        }

    def _clean_title(self, title: str, primary_keyword: str) -> str:
        """Clean title from repetitive keyword usage"""
        if not title:
            return f"Guía Completa de {primary_keyword}"

        # Remove repetitive keyword patterns
        cleaned = title.replace(f"la {primary_keyword},", primary_keyword)
        cleaned = cleaned.replace(f"de la {primary_keyword},", f"de {primary_keyword}")
        cleaned = cleaned.replace(f"{primary_keyword},", primary_keyword)

        return cleaned.strip()

    def _clean_text_content(self, content: str, primary_keyword: str) -> str:
        """Clean text content from repetitive patterns"""
        if not content:
            return ""

        # Remove repetitive keyword patterns
        cleaned = content.replace(f"la {primary_keyword},", primary_keyword)
        cleaned = cleaned.replace(f"de la {primary_keyword},", f"de {primary_keyword}")
        cleaned = cleaned.replace(f"{primary_keyword},", primary_keyword)

        # Remove placeholder text
        placeholders = [
            "Contenido detallado sobre",
            "Esta sección incluye información específica",
            "ejemplos prácticos para implementar esta estrategia exitosamente"
        ]

        for placeholder in placeholders:
            if placeholder in cleaned:
                # If it's mostly placeholder text, replace with meaningful content
                if len(cleaned.replace(placeholder, "").strip()) < 50:
                    cleaned = f"En esta sección exploraremos los aspectos fundamentales de {primary_keyword} y cómo implementarlo efectivamente en tu organización."
                else:
                    cleaned = cleaned.replace(placeholder, "")

        return cleaned.strip()

    def _clean_section(self, section, primary_keyword: str) -> Dict[str, Any]:
        """Clean individual section"""
        if not section or not section.content:
            return None

        # Check if it's a placeholder section
        placeholder_titles = [
            "Beneficio 2 + caso de uso",
            "Beneficio 3 + ejemplo real",
            "Paso 2 (con ejemplos visuales)",
            "Paso 3 (con métricas de éxito)"
        ]

        if section.title in placeholder_titles:
            return None  # Skip placeholder sections

        # Clean section content
        cleaned_content = self._clean_text_content(section.content, primary_keyword)

        # If content is too short or empty after cleaning, skip
        if len(cleaned_content.strip()) < 100:
            return None

        return {
            "title": self._clean_title(section.title, primary_keyword),
            "content": cleaned_content,
            "level": getattr(section, 'level', 2)
        }

    def _clean_faq(self, faq_list: List[Dict], primary_keyword: str) -> List[Dict]:
        """Clean FAQ content"""
        if not faq_list:
            return []

        cleaned_faq = []
        for faq_item in faq_list:
            if faq_item.get('question') and faq_item.get('answer'):
                cleaned_question = self._clean_text_content(faq_item['question'], primary_keyword)
                cleaned_answer = self._clean_text_content(faq_item['answer'], primary_keyword)

                if len(cleaned_answer.strip()) > 20:  # Only include substantial answers
                    cleaned_faq.append({
                        "question": cleaned_question,
                        "answer": cleaned_answer
                    })

        return cleaned_faq

# Factory function for easy instantiation
def create_advanced_blog_generator(gemini_api_key: str = None) -> AdvancedBlogGenerator:
    """Create an instance of the Advanced Blog Generator"""
    return AdvancedBlogGenerator(gemini_api_key)
