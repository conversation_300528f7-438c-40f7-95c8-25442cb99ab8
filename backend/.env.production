# Emma Studio - Production Environment (Cloud-Native)
# NO Docker dependencies, pure cloud services

# Core AI Services
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Cloud Search Services (replace SearXNG Docker)
SERPER_API_KEY=your_serper_api_key_here
GOOGLE_CSE_ID=your_google_cse_id_here
GOOGLE_API_KEY=your_google_api_key_here

# Cloud Cache (replace Red<PERSON> Docker)
UPSTASH_REDIS_URL=your_upstash_redis_url_here

# Cloud Storage
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_S3_BUCKET=your_s3_bucket_name
AWS_REGION=us-east-1

# Cloud Browser Service (replace local Chrome)
BROWSERLESS_API_KEY=your_browserless_api_key

# Application Settings
ENVIRONMENT=production
DEBUG=false
HOST=0.0.0.0
PORT=8001

# CORS Settings for production
ALLOWED_ORIGINS=["https://yourdomain.com", "https://www.yourdomain.com"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
ALLOWED_HEADERS=["*"]

# Database (if needed)
DATABASE_URL=your_production_database_url

# Security
SECRET_KEY=your_super_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Performance
MAX_WORKERS=4
TIMEOUT_SECONDS=30
MAX_CONCURRENT_REQUESTS=10

# Feature Flags
# AgenticSeek removed - integration eliminated
DISABLE_DOCKER_SERVICES=true
DISABLE_HEAVY_MODELS=true
