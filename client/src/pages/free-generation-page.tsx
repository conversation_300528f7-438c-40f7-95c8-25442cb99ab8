import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import {
  Zap,
  Sparkles,
  Upload,
  Wand2,
  Image as ImageIcon,
  Type,
  Palette,
  Target,
  Rocket,
  Brain,
  Star,
  Lightbulb,
  X,
  Download,
  Eye
} from 'lucide-react';
import { Save } from 'lucide-react';

import { AdMiniEditor } from '@/components/tools/ad-creator/shared/AdMiniEditor';
import { GeneratedAd } from '@/types/ad-creator-types';
import { Switch } from '@/components/ui/switch';

// Debugging
const FG_LOG = '[FREE-GEN-SAVE]'
const FG_DBG = import.meta?.env?.DEV || import.meta?.env?.VITE_LOG_LEVEL === 'debug'

import { Label } from '@/components/ui/label';
import { AdsTemplatesService } from '@/services/ads-templates-supabase-service';


function FreeGenerationContent() {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [generatedResult, setGeneratedResult] = useState<any>(null);
  const [useProductImage, setUseProductImage] = useState(true); // Default to REMIX mode
  const [isImprovingPrompt, setIsImprovingPrompt] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const [, navigate] = useLocation();

  // Estados para el mini editor
  const [editingImageUrl, setEditingImageUrl] = useState<string | null>(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);

  // Estado para carpetas y selección por defecto (como Polotno)
  // Modal Guardar en Biblioteca
  const [folderSelectOpen, setFolderSelectOpen] = useState(false);
  const [customName, setCustomName] = useState<string>("");
  const [saving, setSaving] = useState(false);

  const [folders, setFolders] = useState<any[]>([]);
  const [selectedFolderId, setSelectedFolderId] = useState<string | "">("");


  // Imagen seleccionada para guardar desde el modal
  const [imageUrlToSave, setImageUrlToSave] = useState<string | null>(null);

  // Imágenes guardadas (para mostrar badge en miniaturas)
  const [savedImageUrls, setSavedImageUrls] = useState<Set<string>>(new Set());

  useEffect(() => {
    // Cargar carpetas y preseleccionar última usada de la sesión
    (async () => {
      try {
        const userFolders = await AdsTemplatesService.listFolders();
        setFolders(userFolders);
        const defaultFolder = sessionStorage.getItem('POLOTNO_DEFAULT_FOLDER');
        if (defaultFolder) setSelectedFolderId(defaultFolder);
      } catch (e) {
        console.error('[FREE-GEN] listFolders error', e);
      }
    })();
  }, []);


  // Función para abrir el mini editor
  const handleOpenEditor = async (imageUrl: string) => {
    console.log('🎨 Opening editor for image:', imageUrl);

    // For now, let's try using the proxy URL directly with CORS headers
    console.log('🎨 Using proxy URL directly:', imageUrl);
    setEditingImageUrl(imageUrl);
    setIsEditorOpen(true);
  };

  // Función para cerrar el mini editor
  const handleCloseEditor = () => {
    setEditingImageUrl(null);
    setIsEditorOpen(false);
  };

  // Función para manejar imagen editada
  const handleEditedImage = (editedAd: GeneratedAd) => {
    console.log('🎨 Imagen editada recibida:', editedAd);
    console.log('🔍 Imagen original que se está editando:', editingImageUrl);

    // Reemplazar la imagen original con la imagen editada en la lista
    if (generatedResult && editingImageUrl) {
      // Encontrar el índice de la imagen original en all_images
      const originalImageIndex = generatedResult.all_images.findIndex(
        (imageUrl: string) => imageUrl === editingImageUrl
      );

      console.log('📍 Índice de imagen original encontrado:', originalImageIndex);

      if (originalImageIndex !== -1) {
        // Crear nueva lista reemplazando la imagen original con la editada
        const updatedAllImages = [...generatedResult.all_images];
        updatedAllImages[originalImageIndex] = editedAd.image_url;

        // Actualizar el resultado con la imagen reemplazada
        const updatedResult = {
          ...generatedResult,
          all_images: updatedAllImages,
          images: [
            ...(generatedResult.images || []),
            {
              url: editedAd.image_url,
              id: editedAd.id,
              edited: true,
              originalId: editedAd.metadata?.originalId,
              replacedIndex: originalImageIndex
            }
          ]
        };

        console.log('📝 Actualizando resultado - imagen reemplazada:', {
          originalIndex: originalImageIndex,
          newImageUrl: editedAd.image_url.substring(0, 100) + '...',
          totalImages: updatedAllImages.length
        });

        setGeneratedResult(updatedResult);
      } else {
        console.error('❌ No se pudo encontrar la imagen original para reemplazar');
        // Fallback: agregar como nueva imagen
        const updatedResult = {
          ...generatedResult,
          all_images: [...generatedResult.all_images, editedAd.image_url],
          images: [
            ...(generatedResult.images || []),
            {
              url: editedAd.image_url,
              id: editedAd.id,
              edited: true,
              originalId: editedAd.metadata?.originalId
            }
          ]
        };
        setGeneratedResult(updatedResult);
      }
    }

    toast({
      title: "🎉 ¡Imagen editada!",
      description: "La imagen ha sido procesada exitosamente con Stability AI",
    });

    handleCloseEditor();
  };

  const handleImageUpload = (files: FileList | null) => {
    if (files) {
      const newImages = Array.from(files).slice(0, 3); // Máximo 3 imágenes
      setUploadedImages(prev => [...prev, ...newImages].slice(0, 3));
    }
  };

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  const improvePrompt = async () => {
    if (!prompt.trim()) {
      toast({
        title: "¡Necesito un prompt!",
        description: "Escribe algo primero para que pueda mejorarlo",
        variant: "destructive"
      });
      return;
    }

    setIsImprovingPrompt(true);

    try {
      const response = await fetch('/api/v1/ad-creator-agent/improve-prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          original_prompt: prompt,
          context: "advertisement_generation",
          target_platform: "instagram"
        })
      });

      if (response.ok) {
        const result = await response.json();
        setPrompt(result.improved_prompt);
        toast({
          title: "✨ Prompt mejorado",
          description: "Emma optimizó tu descripción para mejores resultados"
        });
      } else {
        throw new Error('Error mejorando el prompt');
      }
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: "No pude mejorar el prompt. Inténtalo de nuevo.",
        variant: "destructive"
      });
    } finally {
      setIsImprovingPrompt(false);
    }
  };

  const handleFreeGeneration = async () => {
    if (!prompt.trim() && uploadedImages.length === 0) {
      toast({
        title: "¡Necesito algo!",
        description: "Escribe qué quieres o sube una imagen de tu producto",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Llamar al endpoint de generación libre con Ideogram (3 versiones por defecto)
      const formData = new FormData();
      formData.append('prompt', prompt);
      formData.append('platform', 'instagram'); // Default platform
      formData.append('size', '1024x1024'); // Default size
      formData.append('num_images', '3'); // Generar 3 versiones por defecto
      formData.append('use_product_image', useProductImage.toString()); // REMIX vs STYLE mode

      uploadedImages.forEach((image, index) => {
        formData.append(`image_${index}`, image);
      });

      const response = await fetch('/api/v1/ad-creator-agent/free-generation', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();

        toast({
          title: "🎉 ¡Anuncio Generado con Ideogram!",
          description: "Emma creó tu anuncio automáticamente"
        });

        // Convertir URLs de Ideogram a URLs proxy de Emma para mejor experiencia
        const processedResult = {
          ...result,
          image_url: result.image_url ? `/api/v1/ad-creator-agent/proxy-image?url=${encodeURIComponent(result.image_url)}` : null,
          all_images: result.all_images ? result.all_images.map((url: string) =>
            `/api/v1/ad-creator-agent/proxy-image?url=${encodeURIComponent(url)}`
          ) : []
        };

        // Mostrar el resultado directamente en la página
        setGeneratedResult(processedResult);
      } else {
        // Better error handling for malformed JSON responses
        let errorMessage = 'Error en la generación';
        try {
          const errorText = await response.text();
          console.error('📄 Error response text:', errorText);

          try {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.detail || errorData.message || 'Error en la generación';
          } catch (jsonError) {
            console.error('❌ Failed to parse error response as JSON:', jsonError);
            errorMessage = `Error ${response.status}: ${response.statusText || 'Error del servidor'}`;
            if (errorText && errorText.length < 200) {
              errorMessage += ` - ${errorText}`;
            }
          }
        } catch (textError) {
          console.error('❌ Failed to get error response as text:', textError);
          errorMessage = `Error ${response.status}: ${response.statusText || 'Error del servidor'}`;
        }
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: error.message || "Hubo un problema. Inténtalo de nuevo.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para descargar imagen usando el endpoint de Emma
  const downloadImage = async (originalUrl: string, filename: string = 'emma-ad') => {
    try {
      // Extraer la URL original de Ideogram del proxy
      const urlMatch = originalUrl.match(/url=([^&]+)/);
      const ideogramUrl = urlMatch ? decodeURIComponent(urlMatch[1]) : originalUrl;

      // Usar el endpoint de descarga de Emma
      const downloadUrl = `/api/v1/ad-creator-agent/download-image?url=${encodeURIComponent(ideogramUrl)}`;

      // Crear enlace de descarga
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "✅ Descarga iniciada",
        description: "La imagen se está descargando automáticamente"
      });
    } catch (error) {
      console.error('Error downloading image:', error);
      toast({
        title: "❌ Error de descarga",
        description: "No se pudo descargar la imagen. Inténtalo de nuevo.",
        variant: "destructive"
      });
    }
  };

  // Guardar imagen en la biblioteca (Supabase)
  const saveToLibrary = async (imageUrl: string, filename: string) => {
    const t0 = performance.now()
    if (FG_DBG) console.log(FG_LOG, 'saveToLibrary: called', { imageUrl: imageUrl?.slice(0, 120), filename, selectedFolderId })
    try {
      const res = await fetch(imageUrl);
      const blob = await res.blob();
      const file = new File([blob], `${filename}.png`, { type: blob.type || 'image/png' });

      const payload = {
        file,
        name: filename,
        description: 'Generado con Emma',
        category: 'Free Generation',
        platform: generatedResult?.platform,
        tags: ['free-generation'],
        folder_id: selectedFolderId || null
      } as const
      if (FG_DBG) console.log(FG_LOG, 'uploadTemplate: payload', { ...payload, file: { name: file.name, type: file.type, size: file.size } })

      const saved = await AdsTemplatesService.uploadTemplate(payload as any);
      if (FG_DBG) console.log(FG_LOG, 'uploadTemplate: success', { id: saved?.id, folder_id: saved?.folder_id, name: saved?.name })

      // Persistir última carpeta elegida para la sesión, como en Polotno
      sessionStorage.setItem('POLOTNO_DEFAULT_FOLDER', selectedFolderId || '');

      const folderName = selectedFolderId ? (folders.find((f: any) => f.id === selectedFolderId)?.name || 'Sin Categorizar') : 'Sin Categorizar';

      // Marcar como guardada en esta sesión (para badge)
      setSavedImageUrls(prev => {
        const next = new Set(prev).add(imageUrl)
        if (FG_DBG) console.log(FG_LOG, 'savedImageUrls: updated', { size: next.size })
        return next
      });

      toast({
        title: `Guardado: ${filename}`,
        description: `Carpeta: ${folderName}`
      });
    } catch (e: any) {
      console.error(FG_LOG, 'saveToLibrary error', e?.message || e);
      toast({ title: 'No se pudo guardar', description: String(e?.message || e), variant: 'destructive' });
    } finally {
      if (FG_DBG) console.log(FG_LOG, 'saveToLibrary: done in', Math.round(performance.now() - t0), 'ms')
    }
  };

  const quickPrompts = [
    "Suplemento natural para dolor de cabeza",
    "App móvil innovadora para fitness",
    "Ropa deportiva premium",
    "Curso online de marketing digital",
    "Restaurante de comida saludable",
    "Servicio de consultoría empresarial"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
      {/* Hero Section profesional estilo Emma */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative overflow-hidden rounded-2xl backdrop-blur-xl mb-8 mx-6 mt-6"
      >
        {/* Gradient background Emma */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef] via-[#4f46e5] to-[#dd3a5a] opacity-95"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

        {/* Elementos flotantes decorativos */}
        <motion.div
          className="absolute right-8 top-8 w-20 h-20 bg-white/20 backdrop-blur-md rounded-2xl transform rotate-12 flex items-center justify-center shadow-2xl border border-white/30"
          animate={{ rotate: [12, 18, 12] }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
        >
          <Zap className="w-10 h-10 text-white" />
        </motion.div>

        <motion.div
          className="absolute left-8 bottom-8 w-16 h-16 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center shadow-xl border border-white/30"
          animate={{ y: [0, -10, 0] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
        >
          <Sparkles className="w-8 h-8 text-white animate-pulse" />
        </motion.div>

        <div className="relative px-8 py-12 md:py-16 md:px-12">
          <div className="max-w-4xl mx-auto text-center">
            {/* Badge superior */}
            <motion.span
              className="inline-flex items-center bg-white/20 backdrop-blur-md text-white font-semibold px-6 py-3 rounded-full mb-6 border border-white/30 text-sm"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              whileHover={{ scale: 1.05 }}
            >
              <Rocket className="inline-block w-4 h-4 mr-2" />
              Tecnología Emma Pro • Nivel Agencias
            </motion.span>

            {/* Título principal */}

            <motion.h1
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
              className="text-4xl lg:text-6xl font-black mb-6 leading-tight"
            >
              <span className="text-white">Generador </span>
              <span className="bg-gradient-to-r from-[#dd3a5a] via-[#f472b6] to-[#fbbf24] bg-clip-text text-transparent">
                Premium
              </span>
            </motion.h1>

            {/* Subtítulo */}
            <motion.p
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 }}
              className="text-xl text-white/90 max-w-3xl mx-auto mb-8 leading-relaxed"
            >
              La misma tecnología que usan las agencias top del mundo, ahora en tus manos.
              <br />
              <span className="text-white/70 text-lg">Resultados profesionales en segundos con la IA más avanzada del mercado</span>
            </motion.p>

            {/* Stats profesionales */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto"
            >
              {[
                { value: "Enterprise", label: "Tecnología", icon: Rocket, color: "text-[#dd3a5a]" },
                { value: "Emma AI", label: "Powered", icon: Brain, color: "text-[#fbbf24]" },
                { value: "Agency", label: "Level", icon: Star, color: "text-[#10b981]" },
                { value: "Premium", label: "Results", icon: Zap, color: "text-[#06b6d4]" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05, y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  className="bg-white/20 backdrop-blur-md rounded-xl p-4 border border-white/30 shadow-xl"
                >
                  <stat.icon className={`w-6 h-6 ${stat.color} mx-auto mb-2`} />
                  <div className="text-white font-bold text-lg">{stat.value}</div>
                  <div className="text-white/70 text-sm">{stat.label}</div>

                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-8"
        >

          {/* Input Section con glassmorphism */}
          <Card className="bg-white/70 backdrop-blur-sm border border-gray-200/50 shadow-xl">
            <CardContent className="p-8">
              <div className="space-y-6">
                {/* Text Input */}
                <div>
                  <label className="text-gray-800 font-semibold mb-3 block text-lg">
                    ¿Qué quieres promocionar?
                  </label>
                  <Textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Ej: Suplemento natural para dolor de cabeza, funciona en 15 minutos..."
                    className="bg-white/80 border-gray-300 text-gray-800 placeholder-gray-500 min-h-[120px] resize-none text-lg focus:ring-2 focus:ring-[#3018ef] focus:border-transparent"
                    maxLength={500}
                  />

                  <div className="flex justify-between items-center mt-2">
                    <Button
                      variant="outline"
                      onClick={improvePrompt}
                      disabled={isImprovingPrompt || !prompt.trim()}
                      className="border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef] hover:text-white transition-all duration-300"
                    >
                      <Lightbulb className="w-4 h-4 mr-2" />
                      {isImprovingPrompt ? 'Mejorando...' : 'Mejorar Prompt'}
                    </Button>
                    <div className="text-sm text-gray-500">
                      {prompt.length}/500
                    </div>
                  </div>
                </div>

                {/* Image Upload */}
                <div>
                  <label className="text-gray-800 font-semibold mb-3 block text-lg">
                    O sube imágenes de tu producto (opcional)
                  </label>
                  <div className="flex items-center gap-4">
                    <Button
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      className="border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef] hover:text-white transition-all duration-300 px-6 py-3"
                    >
                      <Upload className="w-5 h-5 mr-2" />
                      Subir Imágenes
                    </Button>
                    <span className="text-gray-600 text-sm">
                      Máximo 3 imágenes
                    </span>
                  </div>

                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleImageUpload(e.target.files)}
                    className="hidden"
                  />

                  {/* Preview Images */}
                  {uploadedImages.length > 0 && (
                    <div className="flex gap-3 mt-4">
                      {uploadedImages.map((image, index) => (
                        <div key={index} className="relative">
                          <img
                            src={URL.createObjectURL(image)}
                            alt={`Preview ${index + 1}`}
                            className="w-24 h-24 object-cover rounded-xl border-2 border-gray-200 shadow-md"
                          />
                          <button
                            onClick={() => removeImage(index)}
                            className="absolute -top-2 -right-2 bg-[#dd3a5a] text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-[#c73650] transition-colors shadow-lg"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Mode Selection - Only show if images are uploaded */}
                  {uploadedImages.length > 0 && (
                    <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="product-mode" className="text-gray-800 font-semibold text-base">
                            ¿Cómo usar tu imagen?
                          </Label>
                          <p className="text-sm text-gray-600 mt-1">
                            {useProductImage
                              ? "🎯 REMIX: Tu producto aparecerá físicamente en el anuncio"
                              : "🎨 ESTILO: Solo usaré el estilo visual como referencia"
                            }
                          </p>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Label htmlFor="product-mode" className="text-sm text-gray-600">
                            Solo estilo
                          </Label>
                          <Switch
                            id="product-mode"
                            checked={useProductImage}
                            onCheckedChange={setUseProductImage}
                            className="data-[state=checked]:bg-[#3018ef]"
                          />
                          <Label htmlFor="product-mode" className="text-sm text-gray-600">
                            Incluir producto
                          </Label>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Prompts */}
          <Card className="bg-white/70 backdrop-blur-sm border border-gray-200/50 shadow-xl">
            <CardContent className="p-8">
              <h3 className="text-gray-800 font-semibold mb-4 text-lg">💡 Ideas rápidas:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {quickPrompts.map((quickPrompt, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    onClick={() => setPrompt(quickPrompt)}
                    className="text-left justify-start text-gray-700 hover:text-[#3018ef] hover:bg-[#3018ef]/10 text-sm h-auto py-4 px-4 border border-gray-200 hover:border-[#3018ef] transition-all duration-300 rounded-lg min-h-[60px] whitespace-normal leading-relaxed"
                  >
                    <span className="block w-full text-left break-words">
                      {quickPrompt}
                    </span>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Generate Button */}
          <div className="text-center">
            <Button
              onClick={handleFreeGeneration}
              disabled={isGenerating || (!prompt.trim() && uploadedImages.length === 0)}
              className="bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] hover:from-[#c73650] hover:to-[#2516d6] text-white px-16 py-6 text-xl font-bold rounded-full shadow-2xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isGenerating ? (
                <>
                  <Sparkles className="w-7 h-7 mr-4 animate-spin" />
                  Emma está creando tu anuncio...
                </>
              ) : (
                <>
                  <Wand2 className="w-7 h-7 mr-4" />
                  Generar Nivel Premium
                  <Zap className="w-7 h-7 ml-4" />
                </>
              )}
            </Button>

            {!isGenerating && (
              <p className="text-gray-600 text-base mt-4 max-w-md mx-auto">
                Tecnología enterprise que genera campañas de calidad profesional instantáneamente
              </p>
            )}
          </div>

          {/* Generated Result */}
          {generatedResult && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-8"
            >
              <Card className="bg-white/70 backdrop-blur-sm border border-gray-200/50 shadow-xl">
                <CardContent className="p-8">
                  <div className="text-center mb-6">
                    <h3 className="text-3xl font-bold text-gray-800 mb-3">
                      🚀 ¡Campaña Premium Lista!
                    </h3>
                    <p className="text-gray-600 text-lg">
                      Generado con tecnología Emma Enterprise - Calidad nivel agencias
                    </p>
                  </div>

                  {/* Mostrar todas las versiones generadas */}
                  {generatedResult.all_images && generatedResult.all_images.length > 0 && (
                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-gray-800 mb-4 text-center">
                        🎨 {generatedResult.num_generated || generatedResult.all_images.length} Versiones Premium Generadas
                      </h4>
                      <div className={`grid gap-4 ${
                        generatedResult.all_images.length === 1 ? 'grid-cols-1 justify-center' :
                        generatedResult.all_images.length === 2 ? 'grid-cols-1 md:grid-cols-2' :
                        'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                      }`}>
                        {generatedResult.all_images.map((imageUrl: string, index: number) => {
                          // Verificar si esta imagen es una versión editada
                          const editedImageInfo = generatedResult.images?.find((img: any) =>
                            img.url === imageUrl && img.edited
                          );
                          const isEditedImage = !!editedImageInfo;

                          return (
                            <div key={`${index}-${isEditedImage ? 'edited' : 'original'}`} className="relative group">
                              <img
                                src={imageUrl}
                                alt={isEditedImage ? `Versión ${index + 1} - Editada` : `Versión ${index + 1}`}
                                className="w-full rounded-xl shadow-lg border-2 border-gray-200 hover:shadow-2xl transition-all duration-300"
                              />
                              {savedImageUrls.has(imageUrl) && (
                                <div className="absolute bottom-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded-full shadow">
                                  Guardado
                                </div>
                              )}
                              <div className={`absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-semibold ${
                                isEditedImage
                                  ? 'bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] text-white'
                                  : 'bg-[#3018ef] text-white'
                              }`}>
                                {isEditedImage ? '✨ Editada' : `V${index + 1}`}
                              </div>

                              {/* Indicador adicional para imágenes editadas */}
                              {isEditedImage && (
                                <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                  🎯 Borrado IA
                                </div>
                              )}

                            {/* Overlay con botones de acción */}
                            <div className="absolute inset-0 bg-black/0 hover:bg-black/20 rounded-xl transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                              <div className="flex gap-2">
                                <button
                                  onClick={() => downloadImage(imageUrl, `emma-ad-v${index + 1}`)}
                                  className="bg-white/90 backdrop-blur-sm hover:bg-white text-gray-800 px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-1 shadow-lg"
                                >
                                  <Download className="w-4 h-4" />
                                  Descargar
                                </button>
                                <button
                                  onClick={() => {
                                    // Abrir en modal o nueva ventana para vista ampliada
                                    const newWindow = window.open('', '_blank');
                                    if (newWindow) {
                                      newWindow.document.write(`
                                        <html>
                                          <head><title>Emma Ad - Versión ${index + 1}</title></head>
                                          <body style=\"margin:0;background:#000;display:flex;align-items:center;justify-content:center;min-height:100vh;\">\n                                            <img src=\"${imageUrl}\" style=\"max-width:100%;max-height:100%;object-fit:contain;\" alt=\"Emma Ad V${index + 1}\" />\n                                          </body>
                                        </html>
                                      `);
                                    }
                                  }}
                                  className="bg-white/90 backdrop-blur-sm hover:bg-white text-gray-800 px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-1 shadow-lg"
                                >
                                  <Eye className="w-4 h-4" />
                                  Ver
                                </button>
                                <button
                                  onClick={() => {
                                    // Abrir mini editor modal
                                    handleOpenEditor(imageUrl);
                                  }}
                                  className="bg-white/90 backdrop-blur-sm hover:bg-white text-gray-800 px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-1 shadow-lg"
                                >
                                  <Wand2 className="w-4 h-4" />
                                  Editar
                                </button>
                                <button
                                  onClick={() => { setFolderSelectOpen(true); /* imagen seleccionada para modal */ setImageUrlToSave(imageUrl); setCustomName(`emma-ad-v${index + 1}`); }}
                                  className="bg-white/90 backdrop-blur-sm hover:bg-white text-gray-800 px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-1 shadow-lg"
                                >
                                  <Save className="w-4 h-4" />
                                  Guardar
                                </button>
                              </div>
                            </div>
                          </div>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  <div className="text-center space-y-6">
                    <div className="text-sm text-gray-600 bg-gray-50 rounded-lg p-4">
                      <p className="mb-2"><strong>Prompt usado:</strong> {generatedResult.prompt_used}</p>
                      <p className="mb-2"><strong>Plataforma:</strong> {generatedResult.platform}</p>
                      <p className="mb-2"><strong>Tamaño:</strong> {generatedResult.size}</p>
                      <p><strong>Versiones generadas:</strong> {generatedResult.num_generated || 1}</p>
                    </div>

                    <div className="flex gap-4 justify-center flex-wrap items-center">

                      {generatedResult.all_images && generatedResult.all_images.length > 1 && (
                        <Button
                          onClick={() => {
                            // Descargar todas las imágenes
                            generatedResult.all_images.forEach((url: string, index: number) => {
                              setTimeout(() => downloadImage(url, `emma-ad-v${index + 1}`), index * 500);
                            });
                          }}
                          className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 text-white px-6 py-3"
                        >
                          <Download className="w-5 h-5 mr-2" />
                          Descargar Todas
                        </Button>
                      )}

                      <Button
                        onClick={() => {
                          if (generatedResult.image_url) {
                            downloadImage(generatedResult.image_url, 'emma-ad-principal');
                          }
                        }}
                        variant="outline"
                        className="border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef] hover:text-white px-6 py-3"
                      >
                        <Download className="w-5 h-5 mr-2" />
                        Descargar Principal
                      </Button>

                      <Button
                        onClick={() => { setImageUrlToSave(generatedResult?.image_url || null); setCustomName('emma-ad-principal'); setFolderSelectOpen(true); }}
                        className="bg-[#3018ef] text-white px-6 py-3"
                      >
                        <Save className="w-5 h-5 mr-2" />
                        Guardar en biblioteca
                      </Button>

                      {/* Dialog Guardar en biblioteca (estilo Polotno) */}
                      <Dialog open={folderSelectOpen} onOpenChange={setFolderSelectOpen}>
                        <DialogContent className="sm:max-w-md">
                          <DialogHeader>
                            <DialogTitle>Guardar en biblioteca</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-3">
                            <div>
                              <label className="block text-xs text-gray-600 mb-1">Carpeta</label>
                              <Select value={selectedFolderId} onValueChange={setSelectedFolderId}>
                                <SelectTrigger className="h-10">
                                  <SelectValue placeholder="Selecciona carpeta" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="">Sin Categorizar</SelectItem>
                                  {folders.map((f: any) => (
                                    <SelectItem key={f.id} value={f.id}>{f.name}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>

                            <div>
                              <label className="block text-xs text-gray-600 mb-1">Nombre</label>
                              <input
                                value={customName}
                                onChange={(e) => setCustomName(e.target.value)}
                                placeholder="Ej. Campaña Emma"
                                className="w-full h-10 px-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#3018ef]/30"
                              />
                            </div>

                            <div className="flex justify-end gap-2 pt-2">
                              <Button variant="outline" onClick={() => setFolderSelectOpen(false)}>Cancelar</Button>
                              <Button
                                onClick={async () => {
                                  const url = imageUrlToSave || generatedResult?.image_url;
                                  if (!url) return;
                                  setSaving(true);
                                  await saveToLibrary(url, customName?.trim() || 'emma-ad');
                                  setSaving(false);
                                  setFolderSelectOpen(false);
                                  setImageUrlToSave(null);

                                  {/* CTA: Ver en biblioteca */}
                                  <Button
                                    onClick={() => {
                                      const folderParam = selectedFolderId ? `?folderId=${encodeURIComponent(selectedFolderId)}` : ''
                                      window.location.href = `/dashboard/ads-central/templates${folderParam}`
                                    }}
                                    variant="outline"
                                  >
                                    Ver en biblioteca
                                  </Button>

                                }}
                                disabled={saving}
                              >
                                {saving ? 'Guardando...' : 'Guardar'}
                              </Button>
                              <Button
                                onClick={async () => {
                                  const url = imageUrlToSave || generatedResult?.image_url;
                                  if (!url) return;
                                  setSaving(true);
                                  await saveToLibrary(url, customName?.trim() || 'emma-ad');
                                  setSaving(false);
                                  setFolderSelectOpen(false);
                                  setImageUrlToSave(null);
                                  const folderParam = selectedFolderId ? `?folderId=${encodeURIComponent(selectedFolderId)}` : ''
                                  window.location.href = `/dashboard/ads-central/templates${folderParam}`
                                }}
                                disabled={saving}
                              >
                                {saving ? 'Guardando...' : 'Guardar y volver'}
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>


                      <Button
                        onClick={() => {
                          setGeneratedResult(null);
                          setPrompt('');
                          setUploadedImages([]);
                        }}
                        variant="outline"
                        className="border-[#dd3a5a] text-[#dd3a5a] hover:bg-[#dd3a5a] hover:text-white px-6 py-3"
                      >
                        <Sparkles className="w-5 h-5 mr-2" />
                        Nueva Campaña
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Features Premium */}
          {!generatedResult && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-16">
              {[
                { icon: ImageIcon, title: "Enterprise Visual", desc: "Calidad nivel agencias", color: "text-[#dd3a5a]" },
                { icon: Type, title: "Copy Premium", desc: "Headlines que convierten", color: "text-[#3018ef]" },
                { icon: Palette, title: "Brand Design", desc: "Diseño profesional", color: "text-purple-600" },
                { icon: Target, title: "High Performance", desc: "ROI maximizado", color: "text-green-600" }
              ].map((feature, index) => (
                <div key={index} className="text-center p-4 bg-white/50 backdrop-blur-sm rounded-xl border border-gray-200/50 hover:shadow-lg transition-all duration-300">
                  <feature.icon className={`w-10 h-10 ${feature.color} mx-auto mb-3`} />
                  <h4 className="text-gray-800 font-semibold text-base mb-1">{feature.title}</h4>
                  <p className="text-gray-600 text-sm">{feature.desc}</p>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      </div>

      {/* Mini Editor Modal */}
      {isEditorOpen && editingImageUrl && (
        <AdMiniEditor
          ad={{
            id: `free-gen-${Date.now()}`,
            image_url: editingImageUrl,
            prompt: generatedResult?.revised_prompt || 'Imagen generada',
            revised_prompt: generatedResult?.revised_prompt,
            response_id: generatedResult?.id || 'free-gen',
            timestamp: Date.now(),
            metadata: {
              platform: generatedResult?.platform || 'free-generation',
              size: generatedResult?.size || '1024x1024',
              source: 'free-generation'
            }
          }}
          isOpen={isEditorOpen}
          onClose={handleCloseEditor}
          onSave={handleEditedImage}
        />
      )}
    </div>
  );
}

// Componente principal con DashboardLayout
export default function FreeGenerationPage() {
  return (
    <DashboardLayout pageTitle="Generación Libre">
      <FreeGenerationContent />
    </DashboardLayout>
  );
}
