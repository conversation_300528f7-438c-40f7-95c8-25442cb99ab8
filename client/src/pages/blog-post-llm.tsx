import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'wouter';
import { motion } from 'framer-motion';
import { ArrowLeft, Share2, Bookmark, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { emmaAiLogo } from '@/assets';
import { LLM_OPTIMIZED_BLOG_POSTS } from '@/data/blog';
import LLMOptimizedBlog from '@/components/blog/LLMOptimizedBlog';

export default function BlogPostLLM() {
  const { slug } = useParams<{ slug: string }>();
  
  // Encontrar el post por slug
  const post = LLM_OPTIMIZED_BLOG_POSTS.find(p => p.slug === slug);
  
  if (!post) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Artí<PERSON>lo no encontrado
          </h1>
          <Link href="/blog">
            <Button className="bg-[#3018ef] hover:bg-[#3018ef]/90">
              Volver al Blog
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  // Generar meta tags dinámicos
  React.useEffect(() => {
    // Actualizar título de la página
    document.title = `${post.title} | Emma Studio Blog`;
    
    // Actualizar meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', post.metaDescription);
    }
    
    // Añadir meta keywords
    let metaKeywords = document.querySelector('meta[name="keywords"]');
    if (!metaKeywords) {
      metaKeywords = document.createElement('meta');
      metaKeywords.setAttribute('name', 'keywords');
      document.head.appendChild(metaKeywords);
    }
    metaKeywords.setAttribute('content', post.tags.join(', '));
    
    // Open Graph tags
    const updateOrCreateMetaTag = (property: string, content: string) => {
      let tag = document.querySelector(`meta[property="${property}"]`);
      if (!tag) {
        tag = document.createElement('meta');
        tag.setAttribute('property', property);
        document.head.appendChild(tag);
      }
      tag.setAttribute('content', content);
    };
    
    updateOrCreateMetaTag('og:title', post.title);
    updateOrCreateMetaTag('og:description', post.metaDescription);
    updateOrCreateMetaTag('og:image', post.featuredImage.url);
    updateOrCreateMetaTag('og:type', 'article');
    updateOrCreateMetaTag('og:url', `https://www.emmaai.app/blog/${post.slug}`);
    
    // Twitter Card tags
    updateOrCreateMetaTag('twitter:card', 'summary_large_image');
    updateOrCreateMetaTag('twitter:title', post.title);
    updateOrCreateMetaTag('twitter:description', post.metaDescription);
    updateOrCreateMetaTag('twitter:image', post.featuredImage.url);
    
    // Article specific tags
    updateOrCreateMetaTag('article:published_time', post.publishDate);
    updateOrCreateMetaTag('article:modified_time', post.lastModified);
    updateOrCreateMetaTag('article:author', post.author.name);
    updateOrCreateMetaTag('article:section', post.category);
    
    // Canonical URL
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.setAttribute('rel', 'canonical');
      document.head.appendChild(canonical);
    }
    canonical.setAttribute('href', `https://www.emmaai.app/blog/${post.slug}`);
    
  }, [post]);

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          text: post.metaDescription,
          url: window.location.href,
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      // Fallback: copiar URL al clipboard
      navigator.clipboard.writeText(window.location.href);
      // Aquí podrías mostrar un toast de confirmación
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation Header */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50 backdrop-blur-md bg-white/95">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-3">
              <img
                src={emmaAiLogo}
                alt="Emma Studio"
                className="w-12 h-12 object-contain"
              />
              <div className="flex flex-col">
                <span className="text-xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                  Emma Studio
                </span>
                <span className="text-sm text-gray-600 font-medium">Blog</span>
              </div>
            </Link>

            {/* Navigation Actions */}
            <div className="flex items-center space-x-4">
              <Link href="/blog">
                <Button variant="ghost" className="flex items-center">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Volver al Blog
                </Button>
              </Link>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleShare}
                className="flex items-center"
              >
                <Share2 className="w-4 h-4 mr-2" />
                Compartir
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Main Article */}
            <div className="lg:col-span-3">
              <LLMOptimizedBlog post={post} />
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-24 space-y-6">
                {/* Table of Contents */}
                <Card className="border-0 shadow-lg rounded-2xl">
                  <CardContent className="p-6">
                    <h3 className="font-bold text-gray-900 mb-4">
                      Contenido del Artículo
                    </h3>
                    <nav className="space-y-2">
                      <a href="#tldr" className="block text-sm text-gray-600 hover:text-[#3018ef] transition-colors">
                        TL;DR - Resumen Ejecutivo
                      </a>
                      <a href="#respuestas-directas" className="block text-sm text-gray-600 hover:text-[#3018ef] transition-colors">
                        Respuestas Directas
                      </a>
                      <a href="#estadisticas" className="block text-sm text-gray-600 hover:text-[#3018ef] transition-colors">
                        Datos y Estadísticas
                      </a>
                      <a href="#faq" className="block text-sm text-gray-600 hover:text-[#3018ef] transition-colors">
                        Preguntas Frecuentes
                      </a>
                      <a href="#conclusion" className="block text-sm text-gray-600 hover:text-[#3018ef] transition-colors">
                        Conclusión
                      </a>
                    </nav>
                  </CardContent>
                </Card>

                {/* Author Info */}
                <Card className="border-0 shadow-lg rounded-2xl">
                  <CardContent className="p-6">
                    <h3 className="font-bold text-gray-900 mb-4">
                      Sobre el Autor
                    </h3>
                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-900">
                        {post.author.name}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {post.author.role}
                      </p>
                      <p className="text-xs text-gray-500">
                        {post.author.credentials}
                      </p>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        {post.author.bio}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                {/* Related Posts */}
                {post.relatedPosts.length > 0 && (
                  <Card className="border-0 shadow-lg rounded-2xl">
                    <CardContent className="p-6">
                      <h3 className="font-bold text-gray-900 mb-4">
                        Artículos Relacionados
                      </h3>
                      <div className="space-y-3">
                        {post.relatedPosts.map((relatedSlug) => {
                          const relatedPost = LLM_OPTIMIZED_BLOG_POSTS.find(p => p.slug === relatedSlug);
                          if (!relatedPost) return null;
                          
                          return (
                            <Link key={relatedSlug} href={`/blog/${relatedSlug}`}>
                              <div className="block p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                                <h4 className="text-sm font-medium text-gray-900 mb-1 line-clamp-2">
                                  {relatedPost.title}
                                </h4>
                                <p className="text-xs text-gray-500">
                                  {relatedPost.category} • {relatedPost.readTime}
                                </p>
                              </div>
                            </Link>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* CTA Sidebar */}
                <Card className="border-0 shadow-lg rounded-2xl bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] text-white">
                  <CardContent className="p-6 text-center">
                    <h3 className="font-bold mb-3">
                      ¿Te gustó este artículo?
                    </h3>
                    <p className="text-sm opacity-90 mb-4">
                      Descubre más contenido sobre marketing con IA
                    </p>
                    <Link href="/dashboard">
                      <Button className="bg-white text-[#3018ef] hover:bg-gray-100 w-full">
                        Probar Emma Studio
                        <ExternalLink className="w-4 h-4 ml-2" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-50 border-t border-gray-200 py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <Link href="/" className="inline-flex items-center space-x-3 mb-4">
              <img
                src={emmaAiLogo}
                alt="Emma Studio"
                className="w-10 h-10 object-contain"
              />
              <span className="text-lg font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                Emma Studio
              </span>
            </Link>
            <p className="text-gray-600 text-sm">
              © 2025 Emma Studio. Todos los derechos reservados.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
