import type { LLMOptimizedBlogPost } from '../types';

// Extracted from existing big file; shortened to stay under 300 lines in this stub.
// Note: This module should include the 10 posts of Visibilidad/SEO para LLMs.

export { SEO_LLM_P1 as SEO_LLM_POSTS_PART_1 } from './seo-llm/p1';
export { SEO_LLM_P2 as SEO_LLM_POSTS_PART_2 } from './seo-llm/p2';
export { SEO_LLM_P3 as SEO_LLM_POSTS_PART_3 } from './seo-llm/p3';
export { SEO_LLM_P4 as SEO_LLM_POSTS_PART_4 } from './seo-llm/p4';
export { SEO_LLM_P5 as SEO_LLM_POSTS_PART_5 } from './seo-llm/p5';

export const SEO_LLM_POSTS: LLMOptimizedBlogPost[] = [
  ...SEO_LLM_POSTS_PART_1,
  ...SEO_LLM_POSTS_PART_2,
  ...SEO_LLM_POSTS_PART_3,
  ...SEO_LLM_POSTS_PART_4,
  ...SEO_LLM_POSTS_PART_5,
];

