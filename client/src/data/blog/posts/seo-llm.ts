import type { LLMOptimizedBlogPost } from '../types';
import { SEO_LLM_P1 } from './seo-llm/p1';
import { SEO_LLM_P2 } from './seo-llm/p2';
import { SEO_LLM_P3 } from './seo-llm/p3';
import { SEO_LLM_P4 } from './seo-llm/p4';
import { SEO_LLM_P5 } from './seo-llm/p5';

// Extracted from existing big file; shortened to stay under 300 lines in this stub.
// Note: This module should include the 10 posts of Visibilidad/SEO para LLMs.

export { SEO_LLM_P1 as SEO_LLM_POSTS_PART_1 } from './seo-llm/p1';
export { SEO_LLM_P2 as SEO_LLM_POSTS_PART_2 } from './seo-llm/p2';
export { SEO_LLM_P3 as SEO_LLM_POSTS_PART_3 } from './seo-llm/p3';
export { SEO_LLM_P4 as SEO_LLM_POSTS_PART_4 } from './seo-llm/p4';
export { SEO_LLM_P5 as SEO_LLM_POSTS_PART_5 } from './seo-llm/p5';

export const SEO_LLM_POSTS: LLMOptimizedBlogPost[] = [
  ...SEO_LLM_P1,
  ...SEO_LLM_P2,
  ...SEO_LLM_P3,
  ...SEO_LLM_P4,
  ...SEO_LLM_P5,
];

