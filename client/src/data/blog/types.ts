// Shared blog types for modular blog data

export interface LLMOptimizedBlogPost {
  id: string;
  slug: string;
  title: string;
  metaDescription: string;
  tldr: {
    summary: string;
    keywords: string[];
  };
  author: {
    name: string;
    role: string;
    credentials: string;
    bio: string;
  };
  publishDate: string;
  lastModified: string;
  readTime: string;
  category: string;
  tags: string[];
  featuredImage: {
    url: string;
    alt: string;
    caption: string;
  };
  content: {
    introduction: string;
    sections: BlogSection[];
    conclusion: string;
  };
  directAnswers: DirectAnswer[];
  faq: FAQItem[];
  statistics: Statistic[];
  relatedPosts: string[];
  schema: {
    article: object;
    faq: object;
    howTo?: object;
  };
}

export interface BlogSection {
  id: string;
  heading: string;
  level: 2 | 3; // H2 or H3
  content: string;
  subsections?: BlogSection[];
  lists?: {
    type: 'numbered' | 'bulleted';
    items: string[];
  };
  table?: {
    headers: string[];
    rows: string[][];
  };
  callout?: {
    type: 'tip' | 'warning' | 'info';
    content: string;
  };
}

export interface DirectAnswer {
  question: string;
  answer: string;
  details?: string;
  keywords: string[];
}

export interface FAQItem {
  question: string;
  answer: string;
  keywords?: string[];
}

export interface Statistic {
  value: string;
  description: string;
  source: string;
  year: string;
}

