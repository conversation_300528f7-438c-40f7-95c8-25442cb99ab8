import { useEffect } from 'react';
import { EMMA_FAQ_SCHEMA } from '@/data/llm-optimized-content';

// Schema.org structured data types for Emma Studio
export interface OrganizationSchema {
  "@context": "https://schema.org";
  "@type": "Organization";
  name: string;
  description: string;
  url: string;
  logo: string;
  foundingDate?: string;
  sameAs?: string[];
  contactPoint?: ContactPoint[];
  address?: PostalAddress;
  offers?: Offer[];
}

export interface SoftwareApplicationSchema {
  "@context": "https://schema.org";
  "@type": "SoftwareApplication";
  name: string;
  description: string;
  url: string;
  applicationCategory: string;
  operatingSystem: string;
  offers: Offer;
  aggregateRating?: AggregateRating;
  screenshot?: string[];
  featureList?: string[];
}

export interface ServiceSchema {
  "@context": "https://schema.org";
  "@type": "Service";
  name: string;
  description: string;
  provider: Organization;
  serviceType: string;
  areaServed: string;
  offers: Offer[];
}

interface ContactPoint {
  "@type": "ContactPoint";
  telephone?: string;
  contactType: string;
  email?: string;
  url?: string;
}

interface PostalAddress {
  "@type": "PostalAddress";
  streetAddress?: string;
  addressLocality?: string;
  addressRegion?: string;
  postalCode?: string;
  addressCountry: string;
}

interface Offer {
  "@type": "Offer";
  name: string;
  description: string;
  price?: string;
  priceCurrency?: string;
  availability: string;
  url?: string;
}

interface AggregateRating {
  "@type": "AggregateRating";
  ratingValue: number;
  reviewCount: number;
  bestRating?: number;
  worstRating?: number;
}

interface Organization {
  "@type": "Organization";
  name: string;
  url: string;
}

// Predefined structured data for Emma Studio
export const EMMA_ORGANIZATION_SCHEMA: OrganizationSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  name: "Emma Studio",
  description: "La primera agencia virtual de marketing con inteligencia artificial. Automatiza tu marketing con agentes IA especializados disponibles 24/7.",
  url: "https://www.emmaai.app",
  logo: "https://www.emmaai.app/emma-logo.png",
  foundingDate: "2024",
  sameAs: [
    "https://twitter.com/EmmaStudio",
    "https://linkedin.com/company/emma-studio",
    "https://facebook.com/emmastudio",
    "https://instagram.com/emmastudio"
  ],
  contactPoint: [
    {
      "@type": "ContactPoint",
      contactType: "customer service",
      email: "<EMAIL>",
      url: "https://www.emmaai.app/contact"
    },
    {
      "@type": "ContactPoint",
      contactType: "sales",
      email: "<EMAIL>",
      url: "https://www.emmaai.app/contact"
    }
  ],
  address: {
    "@type": "PostalAddress",
    addressCountry: "ES"
  },
  offers: [
    {
      "@type": "Offer",
      name: "Emma Studio Pro",
      description: "Plataforma completa de marketing con IA",
      price: "99",
      priceCurrency: "EUR",
      availability: "https://schema.org/InStock",
      url: "https://www.emmaai.app/pricing"
    }
  ]
};

export const EMMA_SOFTWARE_SCHEMA: SoftwareApplicationSchema = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  name: "Emma Studio",
  description: "Plataforma de marketing con inteligencia artificial que automatiza la creación de contenido, gestión de campañas y análisis de resultados.",
  url: "https://www.emmaai.app",
  applicationCategory: "BusinessApplication",
  operatingSystem: "Web Browser",
  offers: {
    "@type": "Offer",
    name: "Emma Studio Subscription",
    description: "Acceso completo a todas las herramientas de marketing con IA",
    price: "99",
    priceCurrency: "EUR",
    availability: "https://schema.org/InStock"
  },
  featureList: [
    "Generación de contenido con IA",
    "Automatización de campañas",
    "Análisis SEO avanzado",
    "Editor de anuncios inteligente",
    "Agentes IA especializados",
    "Herramientas de diseño visual",
    "Optimización automática"
  ],
  screenshot: [
    "https://www.emmaai.app/screenshots/dashboard.jpg",
    "https://www.emmaai.app/screenshots/content-generator.jpg",
    "https://www.emmaai.app/screenshots/seo-analyzer.jpg"
  ]
};

export const EMMA_MARKETING_SERVICE_SCHEMA: ServiceSchema = {
  "@context": "https://schema.org",
  "@type": "Service",
  name: "Servicios de Marketing con IA",
  description: "Servicios completos de marketing digital automatizado con inteligencia artificial",
  provider: {
    "@type": "Organization",
    name: "Emma Studio",
    url: "https://www.emmaai.app"
  },
  serviceType: "Marketing Digital",
  areaServed: "España",
  offers: [
    {
      "@type": "Offer",
      name: "Automatización de Marketing",
      description: "Automatización completa de campañas de marketing",
      availability: "https://schema.org/InStock"
    },
    {
      "@type": "Offer",
      name: "Generación de Contenido IA",
      description: "Creación automática de contenido optimizado",
      availability: "https://schema.org/InStock"
    },
    {
      "@type": "Offer",
      name: "Análisis SEO Avanzado",
      description: "Análisis y optimización SEO con inteligencia artificial",
      availability: "https://schema.org/InStock"
    }
  ]
};

interface StructuredDataProps {
  schema: object | object[];
  id?: string;
}

export function StructuredData({ schema, id = "structured-data" }: StructuredDataProps) {
  useEffect(() => {
    // Remove existing structured data script if it exists
    const existingScript = document.getElementById(id);
    if (existingScript) {
      existingScript.remove();
    }

    // Create new script element
    const script = document.createElement('script');
    script.id = id;
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(Array.isArray(schema) ? schema : [schema]);
    
    // Add to document head
    document.head.appendChild(script);

    // Cleanup function
    return () => {
      const scriptToRemove = document.getElementById(id);
      if (scriptToRemove) {
        scriptToRemove.remove();
      }
    };
  }, [schema, id]);

  return null; // This component doesn't render anything
}

// Predefined structured data components
export function EmmaOrganizationStructuredData() {
  return <StructuredData schema={EMMA_ORGANIZATION_SCHEMA} id="emma-organization" />;
}

export function EmmaSoftwareStructuredData() {
  return <StructuredData schema={EMMA_SOFTWARE_SCHEMA} id="emma-software" />;
}

export function EmmaServiceStructuredData() {
  return <StructuredData schema={EMMA_MARKETING_SERVICE_SCHEMA} id="emma-service" />;
}

// Combined structured data for homepage
export function EmmaHomepageStructuredData() {
  const combinedSchema = [
    EMMA_ORGANIZATION_SCHEMA,
    EMMA_SOFTWARE_SCHEMA,
    EMMA_MARKETING_SERVICE_SCHEMA,
    EMMA_FAQ_SCHEMA
  ];

  return <StructuredData schema={combinedSchema} id="emma-homepage" />;
}

// Utility function to generate FAQ structured data
export function generateFAQSchema(faqs: Array<{question: string, answer: string}>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
}

// Utility function to generate Article structured data
export function generateArticleSchema(article: {
  title: string;
  description: string;
  author: string;
  publishDate: string;
  modifiedDate?: string;
  image?: string;
  url: string;
}) {
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": article.title,
    "description": article.description,
    "author": {
      "@type": "Organization",
      "name": article.author
    },
    "publisher": {
      "@type": "Organization",
      "name": "Emma Studio",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.emmaai.app/emma-logo.png"
      }
    },
    "datePublished": article.publishDate,
    "dateModified": article.modifiedDate || article.publishDate,
    "image": article.image,
    "url": article.url
  };
}

// Schema específico para HowTo - optimizado para LLMs
export function generateHowToSchema(howTo: {
  name: string;
  description: string;
  steps: Array<{
    name: string;
    text: string;
    image?: string;
  }>;
  totalTime?: string;
  estimatedCost?: string;
}) {
  return {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": howTo.name,
    "description": howTo.description,
    "totalTime": howTo.totalTime,
    "estimatedCost": howTo.estimatedCost,
    "step": howTo.steps.map((step, index) => ({
      "@type": "HowToStep",
      "position": index + 1,
      "name": step.name,
      "text": step.text,
      "image": step.image
    }))
  };
}

// Schema para definiciones - ayuda a LLMs con contexto
export function generateDefinitionSchema(definition: {
  term: string;
  definition: string;
  category?: string;
  relatedTerms?: string[];
}) {
  return {
    "@context": "https://schema.org",
    "@type": "DefinedTerm",
    "name": definition.term,
    "description": definition.definition,
    "inDefinedTermSet": definition.category,
    "sameAs": definition.relatedTerms
  };
}
